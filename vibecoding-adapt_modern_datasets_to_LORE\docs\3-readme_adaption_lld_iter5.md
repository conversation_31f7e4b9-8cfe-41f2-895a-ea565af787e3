# LORE-TSR项目TableLabelMe数据格式支持详细设计（迭代5）

## 项目结构与总体设计

### 设计目标
在迭代4配置系统集成的基础上，实现完整的TableLabelMe数据集类和训练流程集成。将TableLabelMe格式的数据加载、预处理和训练流程与现有LORE-TSR系统完全集成，确保算法一致性和性能优化，为迭代6的可视化验证工具奠定坚实基础。

### 核心设计原则
- **完全兼容**：TableLabelMe数据集类与COCO格式接口完全一致，无缝替换
- **算法一致性**：确保两种格式训练结果完全一致，数据预处理流程统一
- **性能优化**：实现高效的数据加载和缓存机制，支持大规模训练
- **模块集成**：充分复用迭代1-4的所有成果，形成完整的端到端解决方案

## 目录结构树 (Directory Tree)

```
LORE-TSR/src/lib/
├── opts.py                              # [现有] 迭代4扩展的参数解析
├── datasets/
│   ├── dataset_factory.py               # [修改] 更新工厂函数，集成真正的TableLabelMe数据集
│   ├── dataset/
│   │   ├── table.py                     # [现有] COCO格式标准数据集
│   │   ├── table_mid.py                 # [现有] COCO格式中等尺寸数据集
│   │   ├── table_small.py               # [现有] COCO格式小尺寸数据集
│   │   └── table_labelmev2.py           # [重写] 完整的TableLabelMe数据集实现
│   ├── sample/
│   │   └── ctdet.py                     # [现有] CenterNet采样逻辑，作为接口参考
│   └── parsers/                         # [现有] 迭代1-3的解析器模块
│       ├── __init__.py                  # [现有] 解析器包初始化
│       ├── base_parser.py               # [现有] 解析器基类
│       ├── tablelabelme_parser.py       # [现有] TableLabelMe格式解析器
│       ├── file_scanner.py              # [现有] 目录扫描模块
│       └── quality_filter.py            # [现有] 质量筛选模块
├── utils/
│   ├── logger_config.py                 # [现有] 迭代3的日志配置模块
│   ├── config_loader.py                 # [现有] 迭代4的配置文件加载模块
│   └── data_converter.py                # [新增] TableLabelMe到LORE-TSR格式转换工具
└── configs/                             # [现有] 迭代4的配置文件目录
    ├── __init__.py                      # [现有] 配置包初始化
    ├── dataset_configs.py               # [现有] 数据集配置文件模板
    └── config_examples.py               # [现有] 配置示例文件
```

## 整体逻辑和交互时序图

### 核心工作流程
迭代5实现完整的TableLabelMe数据集类，与现有训练流程无缝集成，确保端到端的训练能力。

```mermaid
sequenceDiagram
    participant Main as main.py
    participant DF as DatasetFactory
    participant TLDataset as TableLabelMeDataset
    participant Parser as TableLabelMeParser
    participant Scanner as FileScanner
    participant Filter as QualityFilter
    participant Converter as DataConverter
    participant Trainer as CtdetTrainer

    Main->>DF: get_dataset(opt.dataset, opt.task, config)
    
    alt TableLabelMe模式
        DF->>TLDataset: 创建TableLabelMeDataset实例
        TLDataset->>Scanner: 扫描数据目录，构建文件索引
        Scanner-->>TLDataset: 文件映射字典
        TLDataset->>Filter: 质量筛选，过滤无效样本
        Filter-->>TLDataset: 有效样本列表
        TLDataset->>TLDataset: 初始化完成
        TLDataset-->>DF: TableLabelMe数据集实例
    else COCO模式
        DF->>DF: 创建COCO数据集实例
        DF-->>Main: COCO数据集实例
    end
    
    DF-->>Main: 数据集实例
    Main->>Main: 创建DataLoader
    
    loop 训练循环
        Main->>TLDataset: __getitem__(index)
        TLDataset->>Parser: 解析TableLabelMe标注文件
        Parser-->>TLDataset: 解析后的标注数据
        TLDataset->>Converter: 转换为LORE-TSR格式
        Converter-->>TLDataset: 标准化的标注数据
        TLDataset->>TLDataset: 图像预处理和数据增强
        TLDataset->>TLDataset: 生成热力图和回归目标
        TLDataset-->>Main: 训练样本字典
        Main->>Trainer: 训练批次数据
    end
```

## 数据实体结构深化

### TableLabelMe数据集内部数据结构
```python
# TableLabelMe数据集实例的核心数据结构
dataset_instance = {
    "file_index": {
        image_id: {
            "image_path": str,              # 图像文件绝对路径
            "annotation_path": str,         # 标注文件绝对路径
            "part_dir": str,                # 所属part目录
            "dataset_source": str           # 数据源标识
        }
    },
    "valid_samples": [image_id],            # 质量筛选后的有效样本ID列表
    "sample_cache": {                       # 可选的样本缓存
        image_id: {
            "parsed_data": dict,            # 解析后的标注数据
            "converted_data": dict,         # 转换后的LORE-TSR格式数据
            "cache_timestamp": float        # 缓存时间戳
        }
    }
}
```

### 数据转换流程中的数据结构
```python
# TableLabelMe原始标注 → LORE-TSR标准格式的转换
tablelabelme_annotation = {
    "bbox": {"p1": [x1, y1], "p2": [x2, y2], "p3": [x3, y3], "p4": [x4, y4]},
    "lloc": {"start_row": int, "end_row": int, "start_col": int, "end_col": int},
    "cell_ind": int,
    "quality": str,
    "table_ind": int,
    "type": str,
    "border": dict,
    "content": str
}

# 转换后的LORE-TSR格式
lore_annotation = {
    "image_id": int,                        # 基于文件路径生成的唯一ID
    "annotation_id": int,                   # 全局唯一的标注ID
    "segmentation": [x1, y1, x2, y2, x3, y3, x4, y4],  # 四个角点坐标
    "logic_axis": [start_row, end_row, start_col, end_col],  # 逻辑坐标
    "bbox": [x, y, w, h],                   # 边界框坐标
    "area": float,                          # 区域面积
    "category_id": 1,                       # 类别ID（固定为1）
    "extra_info": dict                      # 额外信息保留
}
```

### 数据实体关系图
```mermaid
erDiagram
    TableLabelMeDataset {
        dict file_index
        list valid_samples
        dict sample_cache
        object parser
        object scanner
        object filter
        object converter
        object opt
        string split
    }
    
    FileIndex {
        int image_id
        string image_path
        string annotation_path
        string part_dir
        string dataset_source
    }
    
    ParsedAnnotation {
        dict bbox_points
        dict logic_coordinates
        int cell_index
        string quality_status
        dict extra_fields
    }
    
    ConvertedAnnotation {
        list segmentation
        list logic_axis
        int image_id
        int annotation_id
        float area
        int category_id
    }
    
    TrainingSample {
        tensor input_image
        tensor hm_heatmap
        tensor wh_bbox
        tensor reg_offset
        tensor logic_axis
        dict meta_info
    }
    
    TableLabelMeDataset ||--|| FileIndex : "包含多个"
    FileIndex ||--|| ParsedAnnotation : "对应"
    ParsedAnnotation ||--|| ConvertedAnnotation : "转换为"
    ConvertedAnnotation ||--|| TrainingSample : "生成"
```

## 配置项

### 数据集配置参数
```python
# 在dataset_configs.py中的TableLabelMe特定配置
TABLELABELME_CONFIG = {
    "cache_enabled": True,                  # 是否启用样本缓存
    "cache_size_limit": 1000,               # 缓存样本数量上限
    "parallel_loading": True,               # 是否启用并行数据加载
    "num_workers": 4,                       # 并行工作进程数
    "quality_threshold": "合格",             # 质量筛选阈值
    "coordinate_validation": True,          # 是否验证坐标有效性
    "image_extensions": [".jpg", ".jpeg", ".png"],  # 支持的图像格式
    "annotation_patterns": [".json", "_table_annotation.json"]  # 标注文件模式
}
```

### 性能优化配置
```python
# 性能相关的可选配置
PERFORMANCE_CONFIG = {
    "preload_annotations": False,           # 是否预加载所有标注
    "memory_limit_mb": 2048,                # 内存使用上限（MB）
    "disk_cache_enabled": False,            # 是否启用磁盘缓存
    "cache_compression": True,              # 缓存数据是否压缩
    "batch_prefetch": 2                     # 批次预取数量
}
```

## 模块化文件详解 (File-by-File Breakdown)

### src/lib/datasets/dataset/table_labelmev2.py
**a. 文件用途说明**
完整的TableLabelMe数据集实现，替换迭代4的占位类。集成迭代1-4的所有成果，实现与COCO格式完全兼容的数据集接口，支持完整的训练流程。

**b. 文件内类图**
```mermaid
classDiagram
    class TableLabelMeDataset {
        +__init__(opt, split)
        +__len__() int
        +__getitem__(index) dict
        +_build_file_index() dict
        +_filter_valid_samples() list
        +_load_and_parse_annotation(image_id) dict
        +_convert_to_lore_format(parsed_data) dict
        +_apply_data_augmentation(image, annotations) tuple
        +_generate_training_targets(annotations) dict
        +_get_image_info(image_id) dict
        +_validate_sample(image_id) bool
        -opt: object
        -split: str
        -parser: TableLabelMeParser
        -scanner: FileScanner
        -filter: QualityFilter
        -converter: DataConverter
        -file_index: dict
        -valid_samples: list
        -sample_cache: dict
    }
    
    class DataConverter {
        +convert_bbox_to_segmentation(bbox_points) list
        +convert_lloc_to_logic_axis(lloc) list
        +generate_image_id(file_path) int
        +generate_annotation_id(image_id, cell_ind) int
        +calculate_area(segmentation) float
        +validate_coordinates(segmentation) bool
    }
    
    TableLabelMeDataset --> DataConverter : uses
    TableLabelMeDataset --> TableLabelMeParser : uses
    TableLabelMeDataset --> FileScanner : uses
    TableLabelMeDataset --> QualityFilter : uses
```

**c. 函数/方法详解**

#### __init__方法
- **用途**: 初始化TableLabelMe数据集实例，集成所有组件
- **输入参数**:
  - `opt`: 配置对象，包含数据路径、训练参数等
  - `split`: 数据集分割标识（'train'或'val'）
- **输出数据结构**: 无返回值，初始化实例属性
- **实现流程**:
```mermaid
flowchart TD
    A[接收opt和split参数] --> B[初始化基础属性]
    B --> C[创建解析器组件]
    C --> D[创建扫描器组件]
    D --> E[创建质量筛选器]
    E --> F[创建数据转换器]
    F --> G[构建文件索引]
    G --> H[执行质量筛选]
    H --> I[初始化样本缓存]
    I --> J[记录初始化完成日志]
```

#### __getitem__方法
- **用途**: 获取指定索引的训练样本，返回与COCO格式完全兼容的数据结构
- **输入参数**:
  - `index`: int - 样本索引
- **输出数据结构**: dict - 训练样本字典，包含input、hm、wh、reg、logic_axis等字段
- **实现流程**:
```mermaid
sequenceDiagram
    participant Dataset as TableLabelMeDataset
    participant Cache as SampleCache
    participant Parser as TableLabelMeParser
    participant Converter as DataConverter
    participant Processor as ImageProcessor

    Dataset->>Dataset: 获取image_id = valid_samples[index]
    Dataset->>Cache: 检查缓存是否存在
    
    alt 缓存命中
        Cache-->>Dataset: 返回缓存的解析数据
    else 缓存未命中
        Dataset->>Parser: 解析标注文件
        Parser-->>Dataset: 原始标注数据
        Dataset->>Converter: 转换为LORE-TSR格式
        Converter-->>Dataset: 标准化标注数据
        Dataset->>Cache: 存储到缓存
    end
    
    Dataset->>Processor: 加载和预处理图像
    Processor-->>Dataset: 处理后的图像张量
    Dataset->>Dataset: 生成热力图和回归目标
    Dataset->>Dataset: 应用数据增强
    Dataset-->>Dataset: 返回完整的训练样本字典
```

#### _convert_to_lore_format方法
- **用途**: 将TableLabelMe格式的标注转换为LORE-TSR内部格式
- **输入参数**:
  - `parsed_data`: dict - 解析后的TableLabelMe标注数据
- **输出数据结构**: dict - LORE-TSR格式的标注数据
- **实现流程**:
```mermaid
flowchart TD
    A[接收TableLabelMe标注数据] --> B[提取bbox.p1-p4坐标]
    B --> C[转换为segmentation格式]
    C --> D[提取lloc逻辑坐标]
    D --> E[转换为logic_axis格式]
    E --> F[生成image_id和annotation_id]
    F --> G[计算area和bbox]
    G --> H[设置category_id为1]
    H --> I[保留额外信息到extra_info]
    I --> J[验证转换结果]
    J --> K[返回LORE-TSR格式数据]
```

### src/lib/utils/data_converter.py
**a. 文件用途说明**
专门负责TableLabelMe格式到LORE-TSR格式转换的工具模块。提供坐标转换、ID生成、数据验证等核心转换功能，确保转换的准确性和一致性。

**b. 文件内类图**
```mermaid
classDiagram
    class DataConverter {
        +__init__(logger)
        +convert_bbox_to_segmentation(bbox_points) list
        +convert_lloc_to_logic_axis(lloc) list
        +generate_image_id(file_path) int
        +generate_annotation_id(image_id, cell_ind) int
        +calculate_area(segmentation) float
        +validate_coordinates(segmentation) bool
        +normalize_coordinates(segmentation, img_width, img_height) list
        +_hash_file_path(file_path) int
        +_validate_bbox_points(bbox_points) bool
        +_validate_logic_coordinates(lloc) bool
        -logger: Logger
    }
```

**c. 函数/方法详解**

#### convert_bbox_to_segmentation方法
- **用途**: 将TableLabelMe的bbox.p1-p4四个角点转换为LORE-TSR的segmentation格式
- **输入参数**:
  - `bbox_points`: dict - 包含p1、p2、p3、p4四个角点的字典
- **输出数据结构**: list - 8个坐标值的一维数组 [x1, y1, x2, y2, x3, y3, x4, y4]
- **实现流程**:
```mermaid
flowchart TD
    A[接收bbox_points字典] --> B[验证四个角点存在性]
    B --> C[提取p1坐标 x1, y1]
    C --> D[提取p2坐标 x2, y2]
    D --> E[提取p3坐标 x3, y3]
    E --> F[提取p4坐标 x4, y4]
    F --> G[按顺序组合为一维数组]
    G --> H[验证坐标有效性]
    H --> I[返回segmentation数组]
```

#### convert_lloc_to_logic_axis方法
- **用途**: 将TableLabelMe的lloc逻辑坐标转换为LORE-TSR的logic_axis格式
- **输入参数**:
  - `lloc`: dict - 包含start_row、end_row、start_col、end_col的字典
- **输出数据结构**: list - 4个逻辑坐标值的数组 [start_row, end_row, start_col, end_col]
- **实现流程**:
```mermaid
flowchart TD
    A[接收lloc字典] --> B[验证必需字段存在性]
    B --> C[提取start_row]
    C --> D[提取end_row]
    D --> E[提取start_col]
    E --> F[提取end_col]
    F --> G[验证逻辑坐标合理性]
    G --> H[组合为logic_axis数组]
    H --> I[返回logic_axis数组]
```

### src/lib/datasets/dataset_factory.py（修改）
**a. 文件用途说明**
更新现有的数据集工厂函数，将迭代4的TableLabelMe占位类替换为迭代5的完整实现，支持真正的TableLabelMe数据集创建和训练。

**b. 主要修改内容**

#### get_dataset方法（更新）
- **用途**: 扩展数据集工厂，支持完整的TableLabelMe数据集创建
- **输入参数**:
  - `dataset`: str - 数据集名称
  - `task`: str - 任务类型
  - `config`: dict - 可选的统一配置对象（迭代4新增）
- **输出数据结构**: class - 完整的TableLabelMe数据集类
- **实现流程**:
```mermaid
flowchart TD
    A[接收参数] --> B{检查config参数}
    B -->|存在config| C{检查dataset_mode}
    B -->|无config| D[使用原有COCO逻辑]
    C -->|TableLabelMe模式| E[导入TableLabelMeDataset]
    C -->|COCO模式| F[使用原有COCO逻辑]
    D --> G[从dataset_factory获取基类]
    F --> G
    E --> H[从_sample_factory获取采样类]
    H --> I[创建TableLabelMe组合类]
    I --> J[返回完整数据集类]
    G --> K[创建COCO组合类]
    K --> L[返回COCO数据集类]
    J --> M[记录创建成功日志]
    L --> M
    M --> N[返回数据集类]
```

## 迭代演进依据

### 架构完整性设计
1. **完整的端到端流程**: 迭代5实现了从数据加载到训练的完整流程，为迭代6的可视化验证提供了完整的数据处理链路
2. **模块化组件集成**: 充分复用迭代1-4的所有成果，形成了完整的模块化架构
3. **性能优化基础**: 实现了缓存机制和并行处理，为大规模训练提供了性能保障
4. **算法一致性保证**: 确保了TableLabelMe和COCO格式的训练结果完全一致

### 后续迭代支持
- **迭代6**: 可视化验证工具可以直接使用迭代5的完整数据集类进行格式转换验证
- **未来扩展**: 数据转换器模块可以扩展支持其他数据格式，架构具有良好的可扩展性
- **性能优化**: 缓存和并行机制为未来的性能优化提供了基础框架

### 技术债务控制
- TableLabelMeDataset类控制在500行以内，功能完整且模块化
- DataConverter模块独立且可复用，便于测试和维护
- 所有新增功能都有完整的错误处理和日志记录
- 与现有COCO格式完全兼容，无破坏性修改

## 如何迁移现有功能

### 代码文件对应关系
| 现有COCO格式功能 | 迭代5实现 | 迁移策略 |
|-----------------|----------|---------|
| `table_mid.py`等数据集类 | `table_labelmev2.py` | 实现相同接口，替换数据源 |
| `ctdet.py`采样逻辑 | 集成到TableLabelMeDataset | 复用现有预处理流程 |
| 数据加载和预处理 | DataConverter + 现有流程 | 格式转换后使用相同逻辑 |
| 训练流程 | 无需修改 | 数据集接口完全兼容 |

### 兼容性保证
- TableLabelMeDataset.__getitem__返回与COCO格式完全相同的数据结构
- 所有现有训练脚本无需任何修改即可使用TableLabelMe格式
- 数据预处理流程完全复用，确保算法一致性
- 错误处理和日志记录与现有系统保持一致

### 验证策略
- 使用相同数据的两种格式进行对比训练，验证结果一致性
- 验证数据加载性能不显著下降
- 测试大规模数据集的处理能力
- 验证多进程数据加载的稳定性

### 迭代5新增功能验收标准
- **功能验收**：
  - TableLabelMe数据集类完整实现，支持完整训练流程
  - 数据格式转换准确性100%，与COCO格式结果一致
  - 集成迭代1-4所有成果，端到端流程正常
  - 性能优化机制有效，支持大规模训练
- **性能验收**：
  - 数据加载性能与COCO格式相当（差异<10%）
  - 支持10万+样本的大规模数据集
  - 内存使用合理，无内存泄漏
  - 多进程数据加载稳定可靠
- **质量验收**：
  - 代码结构清晰，模块化良好
  - 错误处理机制完善，覆盖各种异常情况
  - 算法一致性验证通过，训练结果完全一致
  - 与现有系统完全兼容，无破坏性修改

---

**文档版本**: v5.0
**创建日期**: 2025年7月22日
**迭代范围**: 迭代5 - 训练流程集成和兼容性验证
**依赖迭代**: 基于迭代1-4的完整成果
**后续迭代**: 为迭代6的可视化验证工具提供完整数据处理能力
