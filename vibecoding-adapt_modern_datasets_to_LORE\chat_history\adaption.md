
### 详细设计

#### MVP版本

请根据需求及规划文档 @2-readme_adaption_prdplan.md , 当前LORE-TSR项目的调用链分析文档 @0-readme_LORE_callchain.md ，为我进行详细设计，将结果保存为 @docs/ 目录下的 @3-readme_adaption_lld_iter1.md。 
你先对迭代1进行详细设计，但是整体架构、目录结构得考虑到所有迭代，确保后期易于演进扩展。后期迭代一律用固定返回值的空实现占位。
完成后等待我审核。请你仔细分析、深度思考、尽最大努力进行周密的规划和设计，务必遵循规则 @5-lld.md 

#### 非MVP版本

我已经根据你制定的第一步MVP版本的详细设计文档： @3-readme_adaption_lld_iter1.md 完成了迭代一的开发，最后一小步的报告总结文档为： @step_1_6_report.md , 接下来，请请根据需求及规划文档 @2-readme_adaption_prdplan.md , 当前LORE-TSR项目的调用链分析文档 @0-readme_LORE_callchain.md ，为我进行迭代二的详细设计，将结果保存为 @docs/ 目录下的 @3-readme_adaption_lld_iter2.md。  
整体架构、目录结构x需要考虑到所有迭代，确保后期易于演进扩展。后期迭代一律用固定返回值的空实现占位。 
完成后等待我审核。请你仔细分析、深度思考、尽最大努力进行周密的规划和设计，务必遵循规则 @5-lld.md


我已经根据你制定的第一步MVP版本的详细设计文档： @3-readme_adaption_lld_iter1.md 完成了迭代一的开发，最后一小步的报告总结文档为： @step_1_6_report.md , 接下来，请请根据需求及规划文档 @2-readme_adaption_prdplan.md , 当前LORE-TSR项目的调用链分析文档 @0-readme_LORE_callchain.md ，以及原始COCO格式数据流分析文档： @0-readme_LORE_dataflow.md ，为我进行迭代二的详细设计，将结果保存为 @docs/ 目录下的 @3-readme_adaption_lld_iter2.md。  
整体架构、目录结构x需要考虑到所有迭代，确保后期易于演进扩展。后期迭代一律用固定返回值的空实现占位。 
完成后等待我审核。请你仔细分析、深度思考、尽最大努力进行周密的规划和设计，务必遵循规则 @5-lld.md



### 渐进式小步迭代规划

#### MVP版本

请根据需求文档 @2-readme_adaption_prdplan.md 、MVP版本的详细设计文档 @3-readme_adaption_lld_iter1.md，现有代码调用流程文档 @0-readme_LORE_callchain.md，为我制定迭代一MVP版本的开发步骤，将结果保存为 @docs/ 目录下的  @4-readme_codingplan_codingplan.md。务必遵循规则 @6-codingplan

#### 非MVP版本
我已实现了迭代一MVP版本的开发，最后的小步迭代报告文档为 @step_1_6_report.md，请继续根据需求文档 @2-readme_adaption_prdplan.md 、迭代二版本的详细设计文档 @3-readme_adaption_lld_iter2.md，现有代码调用流程文档 @0-readme_LORE_callchain.md，为我制定迭代二版本的开发步骤，将结果保存为 @docs/ 目录下的  @4-readme_codingplan_codingplan_iter2.md。务必遵循规则 @6-codingplanv5

### 编码实现

#### MVP版本

请根据需求规划文档 @2-readme_adaption_prdplan.md、MVP版本的详细设计文档 @3-readme_adaption_lld_iter1.md, 渐进式小步迭代编码步骤文档 @4-readme_adaption_codingplan_iter1.md，仔细分析、深度思考、尽最大努力制定周密的任务规划，帮助我完成其中的步骤一。务必遵循规则 @7-codingstep.


#### 非MVP版本

请根据需求规划文档 @2-readme_adaption_prdplan.md、迭代二版本的详细设计文档 @3-readme_adaption_lld_iter2.md, 渐进式小步迭代编码步骤文档 @4-readme_adaption_codingplan_iter2.md，仔细分析、深度思考、尽最大努力制定周密的任务规划，帮助我完成其中的步骤一。务必遵循规则 @7-codingstep.



